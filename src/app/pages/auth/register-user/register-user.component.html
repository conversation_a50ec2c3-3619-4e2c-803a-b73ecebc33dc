<!-- Registration Form -->

<!-- Show form when verificationStatusId is 0 and user is not registered -->
<div class="input-card" *ngIf="verificationStatusId === 0 && !registered">
  <div class="m-auto">
    <h2 class="text-dark-50 pb-0 fw-bold text-center">{{ "registerUser.title" | translate }}</h2>
    <p class="text-muted mb-4 text-center">{{ "registerUser.description" | translate }}</p>
  </div>
  <form novalidate (ngSubmit)="onSubmit()">
    <!-- Email Field -->
    <div class="mb-3">
<!--      <label class="form-label">{{ "registerUser.email" | translate }}</label>-->
      <input
        placeholder="{{ 'registerUser.emailLabel' | translate }}"
        [(ngModel)]="form.email"
        name="email"
        autocomplete="email"
        type="text"
        id="email"
        class="form-control"
        required
      />
    </div>

    <!-- Password Field -->
    <div class="mb-3 position-relative">
<!--      <label class="form-label">{{ "registerUser.password" | translate }}</label>-->
      <input
        placeholder="{{ 'registerUser.passwordLabel' | translate }}"
        [(ngModel)]="form.password"
        name="password"
        type="password"
        id="password"
        class="form-control"
        required
      />
      <i
        *ngIf="!passwordVisible"
        class="fa-light fa-eye password-icon pb-2"
        (click)="togglePasswordVisibility('password')"
      ></i>
      <i
        *ngIf="passwordVisible"
        class="fa-light fa-eye-slash password-icon pb-2"
        (click)="togglePasswordVisibility('password')"
      ></i>
    </div>

    <!-- Confirm Password Field -->
    <div class="mb-3 position-relative">
<!--      <label class="form-label">{{ "registerUser.confirmPassword" | translate }}</label>-->
      <input
        placeholder="{{ 'registerUser.confirmPasswordLabel' | translate }}"
        [(ngModel)]="form.confirmPassword"
        name="confirmPassword"
        type="password"
        id="confirmPassword"
        class="form-control"
        required
      />
      <i
        *ngIf="!confirmPasswordVisible"
        class="fa-light fa-eye password-icon pb-2"
        (click)="togglePasswordVisibility('confirmPassword')"
      ></i>
      <i
        *ngIf="confirmPasswordVisible"
        class="fa-light fa-eye-slash password-icon pb-2"
        (click)="togglePasswordVisibility('confirmPassword')"
      ></i>
    </div>

    <!-- Submit Button -->
    <div class="form-group mt-3 text-center">
      <button type="submit" class="w-100 btn btn-primary btn-block">
        {{ "registerUser.submitButtonLabel" | translate }}
      </button>
    </div>
  </form>
</div>

<!-- Show success message when registered -->
<div class="input-card" *ngIf="verificationStatusId === 0 && registered">
  <div class="m-auto">
    <h2 class="text-dark-50 pb-0 fw-bold text-center">{{ "registerUser.title" | translate }}</h2>
    <p class="text-muted mb-4 text-center" style="color: #448C74">
      {{ "registerUser.registered" | translate }}
    </p>

    <!-- Auto-redirect countdown message -->
    <div *ngIf="showAutoRedirect && countdownInterval" class="text-center mb-3">
      <p class="text-muted small">
        {{ "registerUser.autoRedirectMessage" | translate: {seconds: autoRedirectCountdown} }}
      </p>
      <button
        type="button"
        class="btn btn-outline-secondary btn-sm me-2"
        (click)="cancelAutoRedirect()">
        {{ "registerUser.cancelAutoRedirect" | translate }}
      </button>
    </div>

    <!-- Manual redirect message when auto-redirect is disabled -->
    <div *ngIf="!showAutoRedirect || !countdownInterval" class="text-center mb-3">
      <p class="text-muted small">
        {{ "registerUser.manualRedirectMessage" | translate }}
      </p>
    </div>

    <!-- Prominent Continue to Login button -->
    <div class="form-group mt-3 text-center">
      <button
        type="button"
        class="w-100 btn btn-primary btn-block"
        (click)="navigateToLogin()"
        [routerLink]="['/auth/login']">
        {{ "registerUser.continueToLogin" | translate }}
      </button>
    </div>
  </div>
</div>

<!-- Show error message when verificationStatusId is not 0 -->
<div class="input-card" *ngIf="verificationStatusId !== 0">
  <div class="m-auto">
    <h2 class="text-dark-50 text-danger pb-0 fw-bold text-center">
      {{ "registerUser.error.title" | translate }}
    </h2>
    <p class="text-muted mb-4 text-center">
      {{ "registerUser.error.description" | translate }}
    </p>
  </div>
</div>

<!-- Back to Login Link -->
<div *ngIf="!registered" class="mt-3 row">
  <div class="text-center col">
    <p class="text-muted">
      {{ "forgotPassword.backToLoginLabel1" | translate }}
      <a class="text-muted ms-1" [routerLink]="['/auth/login']" style="color: #448C74;">
        <b>{{ "forgotPassword.backToLoginLabel2" | translate }}</b>
      </a>
    </p>
  </div>
</div>