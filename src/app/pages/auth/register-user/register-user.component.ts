import {Component, OnInit, On<PERSON><PERSON>roy} from '@angular/core';
import {USM_USR_19} from "../../../@shared/models/input.interfaces";
import {AuthService} from "../../../@core/services/auth.service";
import {ActivatedRoute, Router, RouterLink} from "@angular/router";
import {InvitationTokenVerificationResponse} from "../../../@shared/models/user.interfaces";
import {LoginResponse} from "../../../@shared/models/authentication.interfaces";
import {StandardImports} from "../../../@shared/global_import";

@Component({
    selector: 'app-register-user',
    templateUrl: './register-user.component.html',
    styleUrls: ['./register-user.component.css'],
    standalone: true,
  imports: [StandardImports, RouterLink]
})
export class RegisterUserComponent implements OnInit, OnDestroy {
  form: any = {
    email: null,
    password: null,
    confirmPassword: null,
  }
  invitationToken: string;
  verificationStatusId: number = -1;

  registered = false;
  autoRedirectCountdown = 10; // 10 seconds countdown
  countdownInterval: any;
  showAutoRedirect = true; // Can be toggled to disable auto-redirect

  constructor(private authService: AuthService,
              private route: ActivatedRoute,
              private router: Router) {
  }

  ngOnInit(): void {
    if (this.authService.isLoggedIn()) {
      this.router.navigate(['/dashboard'])
    }
    this.route.queryParams
      .subscribe(params => {
        this.form.email = params['email'];
        this.invitationToken = params['invitation_token'];
      });

    if (this.invitationToken) {
      this.authService.verifyInvitationToken(this.invitationToken).subscribe(
        (response: InvitationTokenVerificationResponse) => {
          this.verificationStatusId = response.status_id;
        }
      )
    }
  }

  onSubmit(): void {
    if (this.form.password === this.form.confirmPassword) {
      const payload: USM_USR_19 = {
        invitation_token: this.invitationToken,
        password: this.form.password,
        email: this.form.email,
      }
      this.authService.registerUser(payload).subscribe({
        next: (response: LoginResponse) => {
          this.registered = true;
          if (this.showAutoRedirect) {
            this.startAutoRedirectCountdown();
          }
        },
        error: (error) => {
          this.verificationStatusId = 3;
        }
      });
    }
  }

  startAutoRedirectCountdown(): void {
    this.countdownInterval = setInterval(() => {
      this.autoRedirectCountdown--;
      if (this.autoRedirectCountdown <= 0) {
        this.navigateToLogin();
      }
    }, 1000);
  }

  navigateToLogin(): void {
    if (this.countdownInterval) {
      clearInterval(this.countdownInterval);
    }
    this.router.navigate(['/auth/login']);
  }

  cancelAutoRedirect(): void {
    if (this.countdownInterval) {
      clearInterval(this.countdownInterval);
      this.countdownInterval = null;
    }
    this.showAutoRedirect = false;
  }

  ngOnDestroy(): void {
    if (this.countdownInterval) {
      clearInterval(this.countdownInterval);
    }
  }

  passwordVisible: boolean = false;
  confirmPasswordVisible: boolean = false;

  togglePasswordVisibility(inputId: string): void {
    if (inputId === 'password') {
        this.passwordVisible = !this.passwordVisible;
    } else if (inputId === 'confirmPassword') {
        this.confirmPasswordVisible = !this.confirmPasswordVisible;
    }

    const inputElement = document.getElementById(inputId) as HTMLInputElement;
    inputElement.type = inputElement.type === 'password' ? 'text' : 'password';
  }

}
